<!-- ========================= SECTION CATEGORIES ========================= -->
<section class="section-categories padding-y-sm bg-light">
<div class="container">
    <header class="section-heading text-center mb-4">
        <h3 class="section-title">تسوق حسب الفئة</h3>
        <p class="text-muted">اختر من مجموعة متنوعة من الفئات</p>
    </header>

    <div class="categories-slider-container">
        <div class="categories-slider" id="categoriesSlider">
            {% if categories %}
                {% for category in categories %}
                <div class="category-item">
                    <a href="{{ category.get_url }}" class="category-link">
                        <div class="category-icon-wrapper">
                            {% if category.image %}
                                <img src="{{ category.image.url }}" alt="{{ category.name }}" class="category-icon">
                            {% else %}
                                <div class="category-icon-placeholder">
                                    <i class="fa fa-tag fa-2x"></i>
                                </div>
                            {% endif %}
                        </div>
                        <h6 class="category-name">{{ category.name }}</h6>
                    </a>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12 text-center">
                    <p class="text-muted">لا توجد فئات متاحة حالياً</p>
                </div>
            {% endif %}
        </div>
        
        <!-- Navigation buttons -->
        <button class="slider-btn slider-btn-prev" id="prevBtn">
            <i class="fa fa-chevron-right"></i>
        </button>
        <button class="slider-btn slider-btn-next" id="nextBtn">
            <i class="fa fa-chevron-left"></i>
        </button>
    </div>
</div>
</section>
<!-- ========================= SECTION CATEGORIES END// ========================= -->
