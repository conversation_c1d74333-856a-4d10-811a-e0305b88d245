// Categories Slider JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const slider = document.getElementById('categoriesSlider');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');

    if (!slider) return;

    let currentTranslate = 0;
    let startX = 0;
    let isDragging = false;
    let animationId = 0;

    // Calculate dimensions
    function calculateDimensions() {
        // Dynamic item width calculation based on screen size
        let itemWidth = 140; // default for desktop

        if (window.innerWidth <= 480) {
            itemWidth = 95; // 80px width + 15px gap
        } else if (window.innerWidth <= 768) {
            itemWidth = 120; // 100px width + 20px gap
        }

        const containerWidth = slider.parentElement.offsetWidth;
        const visibleItems = Math.floor(containerWidth / itemWidth);
        const totalItems = slider.children.length;
        const maxTranslate = Math.max(0, (totalItems - visibleItems) * itemWidth);

        return { itemWidth, visibleItems, totalItems, maxTranslate };
    }

    function updateButtons() {
        if (!prevBtn || !nextBtn) return;

        const { maxTranslate } = calculateDimensions();
        prevBtn.disabled = currentTranslate <= 0;
        nextBtn.disabled = currentTranslate >= maxTranslate;
    }

    function moveSlider(direction) {
        const { itemWidth, maxTranslate } = calculateDimensions();

        // Adjust movement distance based on screen size
        let moveDistance = itemWidth * 2;
        if (window.innerWidth <= 768) {
            moveDistance = itemWidth * 1; // Move one item at a time on mobile
        }

        if (direction === 'next' && currentTranslate < maxTranslate) {
            currentTranslate = Math.min(currentTranslate + moveDistance, maxTranslate);
        } else if (direction === 'prev' && currentTranslate > 0) {
            currentTranslate = Math.max(currentTranslate - moveDistance, 0);
        }

        slider.style.transform = `translateX(-${currentTranslate}px)`;
        updateButtons();
    }

    function setSliderPosition(translate) {
        const { maxTranslate } = calculateDimensions();
        currentTranslate = Math.max(0, Math.min(translate, maxTranslate));
        slider.style.transform = `translateX(-${currentTranslate}px)`;
        updateButtons();
    }

    // Button event listeners
    if (nextBtn) nextBtn.addEventListener('click', () => moveSlider('next'));
    if (prevBtn) prevBtn.addEventListener('click', () => moveSlider('prev'));

    // Enhanced touch/swipe support for mobile
    let initialTranslate = 0;

    function getPositionX(event) {
        if (event.type.includes('mouse')) {
            return event.clientX;
        } else if (event.touches && event.touches.length > 0) {
            return event.touches[0].clientX;
        } else if (event.changedTouches && event.changedTouches.length > 0) {
            return event.changedTouches[0].clientX;
        }
        return 0;
    }

    function dragStart(event) {
        // Always prevent default for touch events to avoid scrolling
        if (event.type.includes('touch')) {
            event.preventDefault();
        } else if (event.type === 'mousedown') {
            event.preventDefault();
        }

        startX = getPositionX(event);
        initialTranslate = currentTranslate;
        isDragging = true;

        slider.style.cursor = 'grabbing';
        slider.style.transition = 'none';

        if (animationId) {
            cancelAnimationFrame(animationId);
        }

        // Debug log for mobile (remove in production)
        // console.log('Drag started:', { startX, initialTranslate, eventType: event.type });
    }

    function dragMove(event) {
        if (!isDragging) return;

        event.preventDefault();

        const currentX = getPositionX(event);
        const diffX = startX - currentX;
        const newTranslate = initialTranslate + diffX;

        // Apply some resistance at the edges
        const { maxTranslate } = calculateDimensions();
        let finalTranslate = newTranslate;

        if (newTranslate < 0) {
            finalTranslate = newTranslate * 0.3; // Resistance when going beyond start
        } else if (newTranslate > maxTranslate) {
            finalTranslate = maxTranslate + (newTranslate - maxTranslate) * 0.3; // Resistance when going beyond end
        }

        slider.style.transform = `translateX(-${finalTranslate}px)`;

        // Debug log for mobile (remove in production)
        // console.log('Drag move:', { currentX, diffX, finalTranslate, maxTranslate });
    }

    function dragEnd(event) {
        if (!isDragging) return;

        isDragging = false;
        slider.style.cursor = 'grab';
        slider.style.transition = 'transform 0.3s ease';

        const currentX = getPositionX(event);
        const diffX = startX - currentX;
        const newTranslate = initialTranslate + diffX;

        // Snap back to valid position
        setSliderPosition(newTranslate);

        // If the movement was significant, prevent click events on category items
        if (Math.abs(diffX) > 10) {
            setTimeout(() => {
                slider.querySelectorAll('.category-link').forEach(link => {
                    link.style.pointerEvents = 'auto';
                });
            }, 100);

            slider.querySelectorAll('.category-link').forEach(link => {
                link.style.pointerEvents = 'none';
            });
        }
    }

    // Mouse events
    slider.addEventListener('mousedown', dragStart);
    document.addEventListener('mousemove', dragMove);
    document.addEventListener('mouseup', dragEnd);

    // Touch events with better handling
    slider.addEventListener('touchstart', dragStart, { passive: false });
    slider.addEventListener('touchmove', dragMove, { passive: false });
    slider.addEventListener('touchend', dragEnd, { passive: false });

    // Additional touch events for better mobile support
    slider.addEventListener('touchcancel', dragEnd, { passive: false });

    // Simple swipe fallback for mobile devices
    let touchStartX = 0;
    let touchEndX = 0;

    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;

        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0) {
                // Swipe left - move next
                moveSlider('next');
            } else {
                // Swipe right - move prev
                moveSlider('prev');
            }
        }
    }

    slider.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
    }, { passive: true });

    slider.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        if (!isDragging) {
            handleSwipe();
        }
    }, { passive: true });

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowLeft') {
            moveSlider('next'); // في RTL، السهم الأيسر يحرك للأمام
        } else if (e.key === 'ArrowRight') {
            moveSlider('prev'); // في RTL، السهم الأيمن يحرك للخلف
        }
    });

    // Window resize handler
    window.addEventListener('resize', () => {
        const { maxTranslate } = calculateDimensions();
        if (currentTranslate > maxTranslate) {
            setSliderPosition(maxTranslate);
        } else {
            updateButtons();
        }
    });

    // Initialize
    updateButtons();
    slider.style.cursor = 'grab';

    // Debug info (remove in production)
    // const { itemWidth, visibleItems, totalItems, maxTranslate } = calculateDimensions();
    // console.log('Slider initialized:', {
    //     itemWidth,
    //     visibleItems,
    //     totalItems,
    //     maxTranslate,
    //     screenWidth: window.innerWidth,
    //     containerWidth: slider.parentElement.offsetWidth
    // });
});
