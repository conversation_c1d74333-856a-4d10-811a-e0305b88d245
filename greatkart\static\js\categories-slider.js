// Categories Slider JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const slider = document.getElementById('categoriesSlider');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');

    if (!slider) return;

    let currentTranslate = 0;
    let startX = 0;
    let isDragging = false;
    let animationId = 0;

    // Calculate dimensions
    function calculateDimensions() {
        // Get actual item dimensions from DOM
        const firstItem = slider.children[0];
        if (!firstItem) return { itemWidth: 140, visibleItems: 0, totalItems: 0, maxTranslate: 0 };

        const itemRect = firstItem.getBoundingClientRect();
        const itemWidth = itemRect.width;

        // Get gap from computed style
        const sliderStyle = window.getComputedStyle(slider);
        const gap = parseInt(sliderStyle.gap) || 20;

        const actualItemWidth = itemWidth + gap;

        // Get container width excluding padding
        const containerRect = slider.parentElement.getBoundingClientRect();
        const containerStyle = window.getComputedStyle(slider.parentElement);
        const paddingLeft = parseInt(containerStyle.paddingLeft) || 0;
        const paddingRight = parseInt(containerStyle.paddingRight) || 0;
        const availableWidth = containerRect.width - paddingLeft - paddingRight;

        const visibleItems = Math.floor(availableWidth / actualItemWidth);
        const totalItems = slider.children.length;
        const maxTranslate = Math.max(0, (totalItems - visibleItems) * actualItemWidth);

        return { itemWidth: actualItemWidth, visibleItems, totalItems, maxTranslate, availableWidth };
    }

    function updateButtons() {
        if (!prevBtn || !nextBtn) return;

        const { maxTranslate } = calculateDimensions();
        prevBtn.disabled = currentTranslate <= 0;
        nextBtn.disabled = currentTranslate >= maxTranslate;

        // Show/hide buttons based on whether scrolling is needed
        if (maxTranslate <= 0) {
            prevBtn.style.display = 'none';
            nextBtn.style.display = 'none';
        } else {
            if (window.innerWidth > 768) { // Only show on desktop
                prevBtn.style.display = 'flex';
                nextBtn.style.display = 'flex';
            }
        }

        console.log('Update buttons:', { currentTranslate, maxTranslate, prevDisabled: prevBtn.disabled, nextDisabled: nextBtn.disabled });
    }

    function moveSlider(direction) {
        const { itemWidth, maxTranslate } = calculateDimensions();

        // Adjust movement distance based on screen size
        let moveDistance = itemWidth;
        if (window.innerWidth > 768) {
            moveDistance = itemWidth * 2; // Move two items at a time on desktop
        }

        if (direction === 'next' && currentTranslate < maxTranslate) {
            currentTranslate = Math.min(currentTranslate + moveDistance, maxTranslate);
        } else if (direction === 'prev' && currentTranslate > 0) {
            currentTranslate = Math.max(currentTranslate - moveDistance, 0);
        }

        slider.style.transform = `translateX(-${currentTranslate}px)`;
        updateButtons();

        // Debug info
        console.log('Move slider:', { direction, currentTranslate, maxTranslate, moveDistance });
    }

    function setSliderPosition(translate) {
        const { maxTranslate } = calculateDimensions();
        currentTranslate = Math.max(0, Math.min(translate, maxTranslate));
        slider.style.transform = `translateX(-${currentTranslate}px)`;
        updateButtons();
    }

    // Button event listeners
    if (nextBtn) nextBtn.addEventListener('click', () => moveSlider('next'));
    if (prevBtn) prevBtn.addEventListener('click', () => moveSlider('prev'));

    // Enhanced touch/swipe support for mobile
    let initialTranslate = 0;

    function getPositionX(event) {
        if (event.type.includes('mouse')) {
            return event.clientX;
        } else if (event.touches && event.touches.length > 0) {
            return event.touches[0].clientX;
        } else if (event.changedTouches && event.changedTouches.length > 0) {
            return event.changedTouches[0].clientX;
        }
        return 0;
    }

    function dragStart(event) {
        // Always prevent default for touch events to avoid scrolling
        if (event.type.includes('touch')) {
            event.preventDefault();
        } else if (event.type === 'mousedown') {
            event.preventDefault();
        }

        startX = getPositionX(event);
        initialTranslate = currentTranslate;
        isDragging = true;

        slider.style.cursor = 'grabbing';
        slider.style.transition = 'none';

        if (animationId) {
            cancelAnimationFrame(animationId);
        }

        // Debug log for mobile (remove in production)
        // console.log('Drag started:', { startX, initialTranslate, eventType: event.type });
    }

    function dragMove(event) {
        if (!isDragging) return;

        event.preventDefault();

        const currentX = getPositionX(event);
        const diffX = startX - currentX;
        const newTranslate = initialTranslate + diffX;

        // Apply some resistance at the edges
        const { maxTranslate } = calculateDimensions();
        let finalTranslate = newTranslate;

        if (newTranslate < 0) {
            finalTranslate = newTranslate * 0.3; // Resistance when going beyond start
        } else if (newTranslate > maxTranslate) {
            finalTranslate = maxTranslate + (newTranslate - maxTranslate) * 0.3; // Resistance when going beyond end
        }

        slider.style.transform = `translateX(-${finalTranslate}px)`;

        // Debug log for mobile (remove in production)
        // console.log('Drag move:', { currentX, diffX, finalTranslate, maxTranslate });
    }

    function dragEnd(event) {
        if (!isDragging) return;

        isDragging = false;
        slider.style.cursor = 'grab';
        slider.style.transition = 'transform 0.3s ease';

        const currentX = getPositionX(event);
        const diffX = startX - currentX;
        const newTranslate = initialTranslate + diffX;

        // Snap back to valid position
        setSliderPosition(newTranslate);

        // If the movement was significant, prevent click events on category items
        if (Math.abs(diffX) > 10) {
            setTimeout(() => {
                slider.querySelectorAll('.category-link').forEach(link => {
                    link.style.pointerEvents = 'auto';
                });
            }, 100);

            slider.querySelectorAll('.category-link').forEach(link => {
                link.style.pointerEvents = 'none';
            });
        }
    }

    // Mouse events for desktop
    slider.addEventListener('mousedown', dragStart);
    document.addEventListener('mousemove', dragMove);
    document.addEventListener('mouseup', dragEnd);

    // Touch events for mobile - simplified approach
    let touchStartX = 0;
    let touchStartY = 0;
    let touchMoved = false;

    slider.addEventListener('touchstart', function(e) {
        touchStartX = e.touches[0].clientX;
        touchStartY = e.touches[0].clientY;
        touchMoved = false;

        // Start drag for smooth movement
        startX = touchStartX;
        initialTranslate = currentTranslate;
        isDragging = true;
        slider.style.transition = 'none';
    }, { passive: false });

    slider.addEventListener('touchmove', function(e) {
        if (!isDragging) return;

        const touchX = e.touches[0].clientX;
        const touchY = e.touches[0].clientY;

        const diffX = Math.abs(touchX - touchStartX);
        const diffY = Math.abs(touchY - touchStartY);

        // If horizontal movement is greater than vertical, handle it
        if (diffX > diffY && diffX > 10) {
            e.preventDefault(); // Prevent page scroll
            touchMoved = true;

            const currentX = touchX;
            const dragDiff = startX - currentX;
            const newTranslate = initialTranslate + dragDiff;

            // Apply resistance at edges
            const { maxTranslate } = calculateDimensions();
            let finalTranslate = newTranslate;

            if (newTranslate < 0) {
                finalTranslate = newTranslate * 0.3;
            } else if (newTranslate > maxTranslate) {
                finalTranslate = maxTranslate + (newTranslate - maxTranslate) * 0.3;
            }

            slider.style.transform = `translateX(-${finalTranslate}px)`;
        }
    }, { passive: false });

    slider.addEventListener('touchend', function(e) {
        if (!isDragging) return;

        isDragging = false;
        slider.style.transition = 'transform 0.3s ease';

        if (touchMoved) {
            const touchEndX = e.changedTouches[0].clientX;
            const diffX = startX - touchEndX;
            const newTranslate = initialTranslate + diffX;

            // Snap to valid position
            setSliderPosition(newTranslate);

            // Prevent click if moved significantly
            if (Math.abs(diffX) > 10) {
                setTimeout(() => {
                    slider.querySelectorAll('.category-link').forEach(link => {
                        link.style.pointerEvents = 'auto';
                    });
                }, 100);

                slider.querySelectorAll('.category-link').forEach(link => {
                    link.style.pointerEvents = 'none';
                });
            }
        }
    }, { passive: false });

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowLeft') {
            moveSlider('next'); // في RTL، السهم الأيسر يحرك للأمام
        } else if (e.key === 'ArrowRight') {
            moveSlider('prev'); // في RTL، السهم الأيمن يحرك للخلف
        }
    });

    // Window resize handler
    window.addEventListener('resize', () => {
        // Reset position on resize to avoid issues
        currentTranslate = 0;
        slider.style.transform = 'translateX(0)';

        // Update buttons after a short delay to ensure DOM is updated
        setTimeout(() => {
            updateButtons();
        }, 100);
    });

    // Initialize after a short delay to ensure DOM is ready
    setTimeout(() => {
        updateButtons();
        slider.style.cursor = 'grab';

        // Debug info
        const { itemWidth, visibleItems, totalItems, maxTranslate, availableWidth } = calculateDimensions();
        console.log('Slider initialized:', {
            itemWidth,
            visibleItems,
            totalItems,
            maxTranslate,
            availableWidth,
            screenWidth: window.innerWidth,
            containerWidth: slider.parentElement.offsetWidth
        });
    }, 100);
});
