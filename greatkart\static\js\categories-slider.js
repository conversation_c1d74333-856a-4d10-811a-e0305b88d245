// Categories Slider JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const slider = document.getElementById('categoriesSlider');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    
    if (!slider) return;
    
    let currentTranslate = 0;
    let startX = 0;
    let isDragging = false;
    let animationId = 0;
    
    // Calculate dimensions
    function calculateDimensions() {
        const itemWidth = 140; // width + gap
        const containerWidth = slider.parentElement.offsetWidth;
        const visibleItems = Math.floor(containerWidth / itemWidth);
        const totalItems = slider.children.length;
        const maxTranslate = Math.max(0, (totalItems - visibleItems) * itemWidth);
        
        return { itemWidth, visibleItems, totalItems, maxTranslate };
    }
    
    function updateButtons() {
        if (!prevBtn || !nextBtn) return;
        
        const { maxTranslate } = calculateDimensions();
        prevBtn.disabled = currentTranslate <= 0;
        nextBtn.disabled = currentTranslate >= maxTranslate;
    }
    
    function moveSlider(direction) {
        const { itemWidth, maxTranslate } = calculateDimensions();
        
        if (direction === 'next' && currentTranslate < maxTranslate) {
            currentTranslate = Math.min(currentTranslate + itemWidth * 2, maxTranslate);
        } else if (direction === 'prev' && currentTranslate > 0) {
            currentTranslate = Math.max(currentTranslate - itemWidth * 2, 0);
        }
        
        slider.style.transform = `translateX(-${currentTranslate}px)`;
        updateButtons();
    }
    
    function setSliderPosition(translate) {
        const { maxTranslate } = calculateDimensions();
        currentTranslate = Math.max(0, Math.min(translate, maxTranslate));
        slider.style.transform = `translateX(-${currentTranslate}px)`;
        updateButtons();
    }
    
    // Button event listeners
    if (nextBtn) nextBtn.addEventListener('click', () => moveSlider('next'));
    if (prevBtn) prevBtn.addEventListener('click', () => moveSlider('prev'));
    
    // Enhanced touch/swipe support for mobile
    let initialTranslate = 0;
    
    function getPositionX(event) {
        return event.type.includes('mouse') ? event.clientX : event.touches[0].clientX;
    }
    
    function dragStart(event) {
        if (event.type === 'mousedown') {
            event.preventDefault();
        }
        
        startX = getPositionX(event);
        initialTranslate = currentTranslate;
        isDragging = true;
        
        slider.style.cursor = 'grabbing';
        slider.style.transition = 'none';
        
        if (animationId) {
            cancelAnimationFrame(animationId);
        }
    }
    
    function dragMove(event) {
        if (!isDragging) return;
        
        event.preventDefault();
        
        const currentX = getPositionX(event);
        const diffX = startX - currentX;
        const newTranslate = initialTranslate + diffX;
        
        // Apply some resistance at the edges
        const { maxTranslate } = calculateDimensions();
        let finalTranslate = newTranslate;
        
        if (newTranslate < 0) {
            finalTranslate = newTranslate * 0.3; // Resistance when going beyond start
        } else if (newTranslate > maxTranslate) {
            finalTranslate = maxTranslate + (newTranslate - maxTranslate) * 0.3; // Resistance when going beyond end
        }
        
        slider.style.transform = `translateX(-${finalTranslate}px)`;
    }
    
    function dragEnd(event) {
        if (!isDragging) return;
        
        isDragging = false;
        slider.style.cursor = 'grab';
        slider.style.transition = 'transform 0.3s ease';
        
        const currentX = getPositionX(event.type.includes('mouse') ? event : event.changedTouches[0]);
        const diffX = startX - currentX;
        const newTranslate = initialTranslate + diffX;
        
        // Snap back to valid position
        setSliderPosition(newTranslate);
        
        // If the movement was significant, prevent click events on category items
        if (Math.abs(diffX) > 10) {
            setTimeout(() => {
                slider.querySelectorAll('.category-link').forEach(link => {
                    link.style.pointerEvents = 'auto';
                });
            }, 100);
            
            slider.querySelectorAll('.category-link').forEach(link => {
                link.style.pointerEvents = 'none';
            });
        }
    }
    
    // Mouse events
    slider.addEventListener('mousedown', dragStart);
    document.addEventListener('mousemove', dragMove);
    document.addEventListener('mouseup', dragEnd);
    
    // Touch events
    slider.addEventListener('touchstart', dragStart, { passive: false });
    slider.addEventListener('touchmove', dragMove, { passive: false });
    slider.addEventListener('touchend', dragEnd);
    
    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowLeft') {
            moveSlider('next'); // في RTL، السهم الأيسر يحرك للأمام
        } else if (e.key === 'ArrowRight') {
            moveSlider('prev'); // في RTL، السهم الأيمن يحرك للخلف
        }
    });
    
    // Window resize handler
    window.addEventListener('resize', () => {
        const { maxTranslate } = calculateDimensions();
        if (currentTranslate > maxTranslate) {
            setSliderPosition(maxTranslate);
        } else {
            updateButtons();
        }
    });
    
    // Initialize
    updateButtons();
    slider.style.cursor = 'grab';
});
