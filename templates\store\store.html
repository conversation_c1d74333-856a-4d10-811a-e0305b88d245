{% extends 'base.html' %}
{% load static %}

{% block title %}متجر | جريت كارت{% endblock %}

{% block extra_css %}
<link href="{% static 'css/categories-slider.css' %}" rel="stylesheet" type="text/css"/>
{% endblock %}

{% block content %}

<!-- ========================= SECTION PAGETOP ========================= -->
<section class="section-pagetop bg">
<div class="container">
    {% if 'search' in request.path %}
        <h2 class="title-page">نتائج البحث</h2>
    {% else %}
        <h2 class="title-page">متجرنا</h2>
    {% endif %}
</div>
</section>

<!-- Include Categories Slider -->
{% include 'includes/categories-slider.html' %}

<!-- ========================= SECTION CONTENT ========================= -->
<section class="section-content padding-y">
<div class="container">

<div class="row">
    <main class="col-12">

        <header class="border-bottom mb-4 pb-3">
            <div class="form-inline">
                <span class="mr-md-auto"><b>{{ product_count }}</b> منتج وجد</span>
            </div>
        </header>

        <div class="row">
            {% if products %}
            {% for product in products %}
            <div class="col-lg-3 col-md-4 col-sm-6">
                <figure class="card card-product-grid">
                    <div class="img-wrap">
                        <a href="{{ product.get_url }}">
                            {% if product.image %}
                                <img src="{{ product.image.url }}" alt="{{ product.name }}">
                            {% else %}
                                <img src="{% static 'images/default-product.png' %}" alt="{{ product.name }}">
                            {% endif %}
                        </a>
                    </div>
                    <figcaption class="info-wrap">
                        <div class="fix-height">
                            <a href="{{ product.get_url }}" class="title">{{ product.name }}</a>
                            <div class="price-wrap mt-2">
                                <span class="price">{{ product.price }} جنيه</span>
                            </div>
                        </div>
                        <a href="{{ product.get_url }}" class="btn btn-block btn-primary">عرض التفاصيل</a>
                    </figcaption>
                </figure>
            </div>
            {% endfor %}
            {% else %}
            <div class="text-center w-100">
                <h2>لم يتم العثور على منتجات!</h2>
            </div>
            {% endif %}
        </div>

        <!-- Pagination -->
        {% if products.has_other_pages %}
        <div class="col-12">
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if products.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ products.previous_page_number }}">&laquo; السابق</a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#">&laquo; السابق</a>
                        </li>
                    {% endif %}

                    {% for i in products.paginator.page_range %}
                        {% if products.number == i %}
                            <li class="page-item active">
                                <a class="page-link" href="#">{{ i }}</a>
                            </li>
                        {% else %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if products.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ products.next_page_number }}">التالي &raquo;</a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#">التالي &raquo;</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </main>
</div>

</div>
</section>

{% endblock %}

{% block extra_js %}
<script src="{% static 'js/categories-slider.js' %}" type="text/javascript"></script>
{% endblock %}