/* Categories Slider Styles */
.section-categories {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.categories-slider-container {
    position: relative;
    overflow: hidden;
    padding: 0 50px;
    min-height: 120px;
}

.categories-slider {
    display: flex;
    gap: 20px;
    transition: transform 0.3s ease;
    padding: 20px 0;
    cursor: grab;
    user-select: none;
    touch-action: pan-x;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.categories-slider:active {
    cursor: grabbing;
}

.category-item {
    flex: 0 0 auto;
    width: 120px;
    text-align: center;
}

.category-link {
    display: block;
    text-decoration: none;
    color: inherit;
    transition: transform 0.3s ease;
}

.category-link:hover {
    text-decoration: none;
    color: inherit;
    transform: translateY(-5px);
}

.category-icon-wrapper {
    width: 80px;
    height: 80px;
    margin: 0 auto 10px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 3px solid #e9ecef;
}

.category-link:hover .category-icon-wrapper {
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #007bff;
    transform: scale(1.05);
}

.category-icon {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 50%;
}

.category-icon-placeholder {
    color: #6c757d;
    transition: color 0.3s ease;
}

.category-link:hover .category-icon-placeholder {
    color: #007bff;
}

.category-name {
    font-size: 14px;
    font-weight: 600;
    margin: 0;
    color: #495057;
    transition: color 0.3s ease;
}

.category-link:hover .category-name {
    color: #007bff;
}

.slider-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.slider-btn:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
}

.slider-btn-prev {
    left: 10px;
}

.slider-btn-next {
    right: 10px;
}

.slider-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.slider-btn:disabled:hover {
    background: white;
    color: #6c757d;
    border-color: #e9ecef;
}

/* Hide buttons on mobile for better touch experience */
@media (max-width: 768px) {
    .slider-btn {
        display: none !important;
    }

    .categories-slider-container {
        padding: 0 20px;
        overflow-x: auto;
        overflow-y: hidden;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .categories-slider-container::-webkit-scrollbar {
        display: none;
    }

    .categories-slider {
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .categories-slider::-webkit-scrollbar {
        display: none;
    }

    .category-item {
        width: 100px;
    }

    .category-icon-wrapper {
        width: 60px;
        height: 60px;
    }

    .category-icon {
        width: 35px;
        height: 35px;
    }

    .category-name {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .categories-slider-container {
        padding: 0 15px;
    }

    .category-item {
        width: 80px;
    }

    .categories-slider {
        gap: 15px;
    }

    .category-icon-wrapper {
        width: 50px;
        height: 50px;
    }

    .category-icon {
        width: 30px;
        height: 30px;
    }

    .category-name {
        font-size: 11px;
    }
}
