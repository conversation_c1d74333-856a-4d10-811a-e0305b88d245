{% extends 'base.html' %}
{% load static %}

{% block title %}جريت كارت | واحدة من أكبر منصات التسوق عبر الإنترنت{% endblock %}

{% block extra_css %}
<style>
/* Categories Slider Styles */
.section-categories {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.categories-slider-container {
    position: relative;
    overflow: hidden;
    padding: 0 50px;
}

.categories-slider {
    display: flex;
    gap: 20px;
    transition: transform 0.3s ease;
    padding: 20px 0;
}

.category-item {
    flex: 0 0 auto;
    width: 120px;
    text-align: center;
}

.category-link {
    display: block;
    text-decoration: none;
    color: inherit;
    transition: transform 0.3s ease;
}

.category-link:hover {
    text-decoration: none;
    color: inherit;
    transform: translateY(-5px);
}

.category-icon-wrapper {
    width: 80px;
    height: 80px;
    margin: 0 auto 10px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 3px solid #e9ecef;
}

.category-link:hover .category-icon-wrapper {
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #007bff;
    transform: scale(1.05);
}

.category-icon {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 50%;
}

.category-icon-placeholder {
    color: #6c757d;
    transition: color 0.3s ease;
}

.category-link:hover .category-icon-placeholder {
    color: #007bff;
}

.category-name {
    font-size: 14px;
    font-weight: 600;
    margin: 0;
    color: #495057;
    transition: color 0.3s ease;
}

.category-link:hover .category-name {
    color: #007bff;
}

.slider-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.slider-btn:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
}

.slider-btn-prev {
    left: 10px;
}

.slider-btn-next {
    right: 10px;
}

.slider-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.slider-btn:disabled:hover {
    background: white;
    color: #6c757d;
    border-color: #e9ecef;
}

/* Responsive Design */
@media (max-width: 768px) {
    .categories-slider-container {
        padding: 0 30px;
    }

    .category-item {
        width: 100px;
    }

    .category-icon-wrapper {
        width: 60px;
        height: 60px;
    }

    .category-icon {
        width: 35px;
        height: 35px;
    }

    .category-name {
        font-size: 12px;
    }

    .slider-btn {
        width: 35px;
        height: 35px;
    }
}

@media (max-width: 480px) {
    .categories-slider-container {
        padding: 0 25px;
    }

    .category-item {
        width: 80px;
    }

    .categories-slider {
        gap: 15px;
    }
}
</style>
{% endblock %}

{% block content %}

<!-- ========================= SECTION MAIN ========================= -->

<section class="section-intro padding-y-sm">
<div class="container">

<div class="intro-banner-wrap">
	<img src="{% static 'images/banners/1.jpg' %}" class="img-fluid rounded">
</div>

</div> <!-- container //  -->
</section>
<!-- ========================= SECTION MAIN END// ========================= -->

<!-- ========================= SECTION CATEGORIES ========================= -->
<section class="section-categories padding-y-sm bg-light">
<div class="container">
    <header class="section-heading text-center mb-4">
        <h3 class="section-title">تسوق حسب الفئة</h3>
        <p class="text-muted">اختر من مجموعة متنوعة من الفئات</p>
    </header>

    <div class="categories-slider-container">
        <div class="categories-slider" id="categoriesSlider">
            {% if categories %}
                {% for category in categories %}
                <div class="category-item">
                    <a href="{{ category.get_url }}" class="category-link">
                        <div class="category-icon-wrapper">
                            {% if category.image %}
                                <img src="{{ category.image.url }}" alt="{{ category.name }}" class="category-icon">
                            {% else %}
                                <div class="category-icon-placeholder">
                                    <i class="fa fa-tag fa-2x"></i>
                                </div>
                            {% endif %}
                        </div>
                        <h6 class="category-name">{{ category.name }}</h6>
                    </a>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12 text-center">
                    <p class="text-muted">لا توجد فئات متاحة حالياً</p>
                </div>
            {% endif %}
        </div>

        <!-- Navigation buttons -->
        <button class="slider-btn slider-btn-prev" id="prevBtn">
            <i class="fa fa-chevron-right"></i>
        </button>
        <button class="slider-btn slider-btn-next" id="nextBtn">
            <i class="fa fa-chevron-left"></i>
        </button>
    </div>
</div>
</section>
<!-- ========================= SECTION CATEGORIES END// ========================= -->

<!-- ========================= SECTION  ========================= -->
<section class="section-name padding-y-sm">
<div class="container">

<header class="section-heading">
	<a href="{% url 'store:store' %}" class="btn btn-outline-primary float-left">عرض الكل</a>
	<h3 class="section-title">المنتجات الشائعة</h3>
</header><!-- sect-heading -->


<div class="row">
	{% if products %}
		{% for product in products %}
		<div class="col-md-3">
			<div class="card card-product-grid">
				<a href="{{ product.get_url }}" class="img-wrap">
					{% if product.image %}
						<img src="{{ product.image.url }}" alt="{{ product.name }}">
					{% else %}
						<img src="{% static 'images/default-product.png' %}" alt="{{ product.name }}">
					{% endif %}
				</a>
				<figcaption class="info-wrap">
					<a href="{{ product.get_url }}" class="title">{{ product.name }}</a>
					<div class="price mt-1">{{ product.price }} جنيه</div>
				</figcaption>
			</div>
		</div>
		{% endfor %}
	{% else %}
		<div class="col-12 text-center">
			<h4>لا توجد منتجات متاحة حالياً</h4>
		</div>
	{% endif %}
</div>
</div><!-- container // -->
</section>
<!-- ========================= SECTION  END// ========================= -->




{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const slider = document.getElementById('categoriesSlider');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');

    if (!slider || !prevBtn || !nextBtn) return;

    let currentTranslate = 0;
    const itemWidth = 140; // width + gap
    const visibleItems = Math.floor(slider.parentElement.offsetWidth / itemWidth);
    const totalItems = slider.children.length;
    const maxTranslate = Math.max(0, (totalItems - visibleItems) * itemWidth);

    function updateButtons() {
        prevBtn.disabled = currentTranslate <= 0;
        nextBtn.disabled = currentTranslate >= maxTranslate;
    }

    function moveSlider(direction) {
        if (direction === 'next' && currentTranslate < maxTranslate) {
            currentTranslate = Math.min(currentTranslate + itemWidth * 2, maxTranslate);
        } else if (direction === 'prev' && currentTranslate > 0) {
            currentTranslate = Math.max(currentTranslate - itemWidth * 2, 0);
        }

        slider.style.transform = `translateX(-${currentTranslate}px)`;
        updateButtons();
    }

    nextBtn.addEventListener('click', () => moveSlider('next'));
    prevBtn.addEventListener('click', () => moveSlider('prev'));

    // Touch/swipe support for mobile
    let startX = 0;
    let isDragging = false;

    slider.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX;
        isDragging = true;
    });

    slider.addEventListener('touchmove', (e) => {
        if (!isDragging) return;
        e.preventDefault();
    });

    slider.addEventListener('touchend', (e) => {
        if (!isDragging) return;

        const endX = e.changedTouches[0].clientX;
        const diff = startX - endX;

        if (Math.abs(diff) > 50) { // minimum swipe distance
            if (diff > 0) {
                moveSlider('next');
            } else {
                moveSlider('prev');
            }
        }

        isDragging = false;
    });

    // Mouse drag support for desktop
    let mouseStartX = 0;
    let isMouseDragging = false;

    slider.addEventListener('mousedown', (e) => {
        mouseStartX = e.clientX;
        isMouseDragging = true;
        slider.style.cursor = 'grabbing';
        e.preventDefault();
    });

    document.addEventListener('mousemove', (e) => {
        if (!isMouseDragging) return;
        e.preventDefault();
    });

    document.addEventListener('mouseup', (e) => {
        if (!isMouseDragging) return;

        const endX = e.clientX;
        const diff = mouseStartX - endX;

        if (Math.abs(diff) > 50) { // minimum drag distance
            if (diff > 0) {
                moveSlider('next');
            } else {
                moveSlider('prev');
            }
        }

        isMouseDragging = false;
        slider.style.cursor = 'grab';
    });

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowLeft') {
            moveSlider('next'); // في RTL، السهم الأيسر يحرك للأمام
        } else if (e.key === 'ArrowRight') {
            moveSlider('prev'); // في RTL، السهم الأيمن يحرك للخلف
        }
    });

    // Window resize handler
    window.addEventListener('resize', () => {
        currentTranslate = 0;
        slider.style.transform = 'translateX(0)';
        updateButtons();
    });

    // Initialize
    updateButtons();
    slider.style.cursor = 'grab';
});
</script>
{% endblock %}